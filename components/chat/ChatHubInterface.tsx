import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import {
  MessageCircle,
  Send,
  Camera,
  Image as ImageIcon,
  Plus,
  Users,
  Package,
  TrendingUp,
  Zap,
  Menu,
  X,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';
import { commonStyles } from '@/styles/commonStyles';
import { useChatStore, ChatMessage } from '@/stores/chat-store';
import { useClientStore } from '@/stores/client-store';
import { useInventoryStore } from '@/stores/inventory-store';
import ConversationsList from './ConversationsList';
import FloatingActionsPanel from './FloatingActionsPanel';
import InlineShortcuts from './InlineShortcuts';
import ContextualCards from './ContextualCards';
import ConversationalReports from './ConversationalReports';
import { useChatHub } from '@/hooks/useChatHub';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SIDEBAR_WIDTH = 320;
const IS_TABLET = SCREEN_WIDTH >= 768;

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  color: string;
  onPress: () => void;
}

export default function ChatHubInterface() {
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [showSidebar, setShowSidebar] = useState(IS_TABLET);

  // Use the custom chat hub hook
  const {
    currentContext,
    showQuickActions,
    showFloatingActions,
    isProcessingAction,
    handleNewService,
    handleQuickPhoto,
    handleClientLookup,
    handleInventoryCheck,
    handleFormulaHelp,
    sendContextualMessage,
    getContextualData,
    setShowQuickActions,
    setShowFloatingActions,
  } = useChatHub();

  const {
    conversations,
    messages,
    activeConversationId,
    isLoading,
    isSending,
    error,
    loadConversations,
    createConversation,
    setActiveConversation,
    sendMessage,
    clearError,
  } = useChatStore();

  const { clients } = useClientStore();
  const { products } = useInventoryStore();

  // Initialize chat store
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  const handleSend = useCallback(async () => {
    if (!message.trim() || isSending) return;

    const messageText = message.trim();
    setMessage('');

    try {
      await sendContextualMessage(messageText);
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'No se pudo enviar el mensaje. Inténtalo de nuevo.');
    }
  }, [message, isSending, sendContextualMessage]);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const handleShortcutPress = (action: string) => {
    sendContextualMessage(action);
  };

  // Quick Actions Configuration
  const quickActions: QuickAction[] = [
    {
      id: 'new-service',
      title: 'Nuevo Servicio',
      subtitle: 'Crear servicio de coloración',
      icon: Zap,
      color: Colors.light.primary,
      onPress: () => {
        setMessage('Quiero crear un nuevo servicio de coloración');
        setShowQuickActions(false);
      },
    },
    {
      id: 'client-lookup',
      title: 'Buscar Cliente',
      subtitle: `${clients.length} clientes registrados`,
      icon: Users,
      color: Colors.light.secondary,
      onPress: () => {
        setMessage('Muéstrame información de mis clientes');
        setShowQuickActions(false);
      },
    },
    {
      id: 'inventory-check',
      title: 'Consultar Inventario',
      subtitle: `${products.length} productos disponibles`,
      icon: Package,
      color: Colors.light.info,
      onPress: () => {
        setMessage('Necesito revisar mi inventario de productos');
        setShowQuickActions(false);
      },
    },
    {
      id: 'formula-help',
      title: 'Ayuda con Fórmula',
      subtitle: 'Consulta técnica de colorimetría',
      icon: TrendingUp,
      color: Colors.light.success,
      onPress: () => {
        setMessage('Necesito ayuda con una fórmula de coloración');
        setShowQuickActions(false);
      },
    },
  ];

  const renderQuickAction = (action: QuickAction) => (
    <TouchableOpacity
      key={action.id}
      style={[styles.quickActionCard, { borderLeftColor: action.color }]}
      onPress={action.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.quickActionIcon, { backgroundColor: action.color + '20' }]}>
        <action.icon size={24} color={action.color} />
      </View>
      <View style={styles.quickActionContent}>
        <Text style={styles.quickActionTitle}>{action.title}</Text>
        <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderMessage = (msg: ChatMessage, index: number) => {
    const isUser = msg.role === 'user';
    return (
      <View
        key={`${msg.id}-${index}`}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        {!isUser && (
          <View style={styles.assistantAvatar}>
            <MessageCircle size={16} color={Colors.light.primary} />
          </View>
        )}
        <View
          style={[
            styles.messageBubble,
            isUser ? styles.userBubble : styles.assistantBubble,
          ]}
        >
          <Text
            style={[
              styles.messageText,
              isUser ? styles.userMessageText : styles.assistantMessageText,
            ]}
          >
            {msg.content}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Sidebar for conversations */}
      {showSidebar && (
        <View style={styles.sidebar}>
          <ConversationsList
            conversations={conversations}
            activeConversationId={activeConversationId}
            onSelectConversation={setActiveConversation}
            onClose={() => setShowSidebar(false)}
          />
        </View>
      )}

      {/* Main chat area */}
      <View style={[styles.mainContent, showSidebar && styles.mainContentWithSidebar]}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top + spacing.md }]}>
          <View style={styles.headerContent}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={toggleSidebar} style={styles.menuButton}>
                <Menu size={20} color={Colors.light.textSecondary} />
              </TouchableOpacity>
            )}
            <Text style={styles.headerTitle}>Salonier</Text>
            <View style={commonStyles.width20} />
          </View>
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {currentMessages.length === 0 ? (
            <View style={styles.emptyState}>
              <View style={styles.welcomeSection}>
                <View style={styles.welcomeIcon}>
                  <MessageCircle size={48} color={Colors.light.primary} />
                </View>
                <Text style={styles.welcomeTitle}>¡Hola! Soy tu asistente experto</Text>
                <Text style={styles.welcomeDescription}>
                  Puedo ayudarte con fórmulas de coloración, diagnósticos capilares, gestión de
                  clientes y cualquier consulta profesional sobre colorimetría.
                </Text>
              </View>

              {/* Quick Actions */}
              {showQuickActions && (
                <View style={styles.quickActionsSection}>
                  <Text style={styles.quickActionsTitle}>¿En qué puedo ayudarte?</Text>
                  <View style={styles.quickActionsGrid}>
                    {quickActions.map(renderQuickAction)}
                  </View>

                  {/* Inline Shortcuts */}
                  <View style={styles.inlineShortcutsSection}>
                    <InlineShortcuts
                      context={currentContext}
                      onShortcutPress={handleShortcutPress}
                      recentClients={clients.slice(0, 3).map(c => ({ id: c.id, name: c.name }))}
                      lowStockProducts={products.filter(p => p.currentStock <= p.minStock).slice(0, 2).map(p => ({
                        id: p.id,
                        name: p.displayName || `${p.brand} ${p.type}`,
                        stock: p.currentStock
                      }))}
                    />
                  </View>
                </View>
              )}
            </View>
          ) : (
            currentMessages.map(renderMessage)
          )}

          {isSending && (
            <View style={styles.typingContainer}>
              <View style={styles.assistantAvatar}>
                <MessageCircle size={16} color={Colors.light.primary} />
              </View>
              <View style={styles.typingBubble}>
                <ActivityIndicator size="small" color={Colors.light.primary} />
                <Text style={styles.typingText}>Escribiendo...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input */}
        <View style={[styles.inputContainer, { paddingBottom: insets.bottom + spacing.md }]}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.input}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
  },
  sidebar: {
    width: SIDEBAR_WIDTH,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
  },
  mainContent: {
    flex: 1,
  },
  mainContentWithSidebar: {
    marginLeft: IS_TABLET ? 0 : -SIDEBAR_WIDTH,
  },
  header: {
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.sm,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuButton: {
    padding: spacing.xs,
  },
  headerTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: spacing['2xl'],
  },
  welcomeIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  welcomeTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  welcomeDescription: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  quickActionsSection: {
    width: '100%',
    maxWidth: 400,
  },
  quickActionsTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  quickActionsGrid: {
    gap: spacing.md,
  },
  inlineShortcutsSection: {
    marginTop: spacing.lg,
  },
  quickActionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    borderLeftWidth: 4,
    ...shadows.sm,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  quickActionContent: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  quickActionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  messageContainer: {
    marginBottom: spacing.md,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  assistantMessageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  assistantAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    marginTop: spacing.xs,
  },
  messageBubble: {
    maxWidth: '85%',
    borderRadius: radius.lg,
    padding: spacing.md,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
  },
  assistantBubble: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  messageText: {
    fontSize: typography.sizes.lg,
    lineHeight: 22,
  },
  userMessageText: {
    color: Colors.light.surface,
  },
  assistantMessageText: {
    color: Colors.light.text,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  typingText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
  },
  inputContainer: {
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
    maxHeight: 100,
    marginRight: spacing.sm,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.light.textSecondary,
  },
});
              value={message}
              onChangeText={setMessage}
              placeholder="Escribe tu consulta..."
              placeholderTextColor={Colors.light.textSecondary}
              multiline
              maxHeight={100}
              editable={!isSending}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!message.trim() || isSending) && styles.sendButtonDisabled,
              ]}
              onPress={handleSend}
              disabled={!message.trim() || isSending}
            >
              {isSending ? (
                <ActivityIndicator size="small" color={Colors.light.surface} />
              ) : (
                <Send size={20} color={Colors.light.surface} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Floating Actions Panel */}
      <FloatingActionsPanel
        onNewService={handleNewService}
        onQuickPhoto={handleQuickPhoto}
        onClientLookup={handleClientLookup}
        onInventoryCheck={handleInventoryCheck}
        onFormulaHelp={handleFormulaHelp}
        isVisible={showFloatingActions && currentMessages.length > 0}
        onToggle={() => setShowFloatingActions(!showFloatingActions)}
      />
    </KeyboardAvoidingView>
  );
}
