import React, { Suspense } from 'react';
import { StyleSheet, View, ActivityIndicator } from 'react-native';
import { useMemoryMonitor } from '@/utils/memory-cleanup';
import { LazyComponents } from '@/utils/lazy-components';
import Colors from '@/constants/colors';

// Fallback component for lazy loading
const ChatLoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.light.primary} />
  </View>
);

export default function AssistantScreen() {
  // Monitor memory usage
  useMemoryMonitor('AssistantScreen');

  return (
    <View style={styles.container}>
      <Suspense fallback={<ChatLoadingFallback />}>
        <ChatHubInterface />
      </Suspense>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
});
