import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Dimensions,
  PanResponder,
} from 'react-native';
import {
  Plus,
  Camera,
  Users,
  Package,
  TrendingUp,
  Zap,
  X,
  ChevronUp,
  ChevronDown,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, shadows, typography } from '@/constants/theme';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface FloatingAction {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  onPress: () => void;
}

interface FloatingActionsPanelProps {
  onNewService: () => void;
  onQuickPhoto: () => void;
  onClientLookup: () => void;
  onInventoryCheck: () => void;
  onFormulaHelp: () => void;
  isVisible: boolean;
  onToggle: () => void;
}

export default function FloatingActionsPanel({
  onNewService,
  onQuickPhoto,
  onClientLookup,
  onInventoryCheck,
  onFormulaHelp,
  isVisible,
  onToggle,
}: FloatingActionsPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const panelOpacity = useRef(new Animated.Value(0)).current;
  const position = useRef(new Animated.ValueXY({ x: SCREEN_WIDTH - 80, y: SCREEN_HEIGHT - 200 })).current;

  const actions: FloatingAction[] = [
    {
      id: 'new-service',
      title: 'Nuevo Servicio',
      icon: Zap,
      color: Colors.light.primary,
      onPress: onNewService,
    },
    {
      id: 'quick-photo',
      title: 'Foto Rápida',
      icon: Camera,
      color: Colors.light.secondary,
      onPress: onQuickPhoto,
    },
    {
      id: 'clients',
      title: 'Clientes',
      icon: Users,
      color: Colors.light.info,
      onPress: onClientLookup,
    },
    {
      id: 'inventory',
      title: 'Inventario',
      icon: Package,
      color: Colors.light.success,
      onPress: onInventoryCheck,
    },
    {
      id: 'formula',
      title: 'Fórmula',
      icon: TrendingUp,
      color: Colors.light.warning,
      onPress: onFormulaHelp,
    },
  ];

  // Pan responder for dragging
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      position.setOffset({
        x: position.x._value,
        y: position.y._value,
      });
    },
    onPanResponderMove: Animated.event(
      [null, { dx: position.x, dy: position.y }],
      { useNativeDriver: false }
    ),
    onPanResponderRelease: () => {
      position.flattenOffset();
      
      // Snap to edges
      const currentX = position.x._value;
      const currentY = position.y._value;
      
      const snapX = currentX > SCREEN_WIDTH / 2 ? SCREEN_WIDTH - 80 : 20;
      const clampedY = Math.max(100, Math.min(SCREEN_HEIGHT - 200, currentY));
      
      Animated.spring(position, {
        toValue: { x: snapX, y: clampedY },
        useNativeDriver: false,
      }).start();
    },
  });

  const toggleExpanded = () => {
    const toValue = isExpanded ? 0 : 1;
    setIsExpanded(!isExpanded);
    
    Animated.parallel([
      Animated.spring(animatedValue, {
        toValue,
        useNativeDriver: false,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(panelOpacity, {
        toValue,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const renderAction = (action: FloatingAction, index: number) => {
    const translateY = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, -(60 * (index + 1))],
    });

    const scale = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    return (
      <Animated.View
        key={action.id}
        style={[
          styles.actionButton,
          {
            transform: [{ translateY }, { scale }],
            backgroundColor: action.color,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.actionTouchable}
          onPress={() => {
            action.onPress();
            toggleExpanded();
          }}
          activeOpacity={0.8}
        >
          <action.icon size={24} color="white" />
        </TouchableOpacity>
        
        {/* Action label */}
        <Animated.View
          style={[
            styles.actionLabel,
            {
              opacity: panelOpacity,
              transform: [
                {
                  translateX: animatedValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -120],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={styles.actionLabelText}>{action.title}</Text>
        </Animated.View>
      </Animated.View>
    );
  };

  if (!isVisible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: position.getTranslateTransform(),
        },
      ]}
      {...panResponder.panHandlers}
    >
      {/* Action buttons */}
      {actions.map((action, index) => renderAction(action, index))}
      
      {/* Main toggle button */}
      <TouchableOpacity
        style={[
          styles.mainButton,
          isExpanded && styles.mainButtonExpanded,
        ]}
        onPress={toggleExpanded}
        activeOpacity={0.8}
      >
        <Animated.View
          style={{
            transform: [
              {
                rotate: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '45deg'],
                }),
              },
            ],
          }}
        >
          <Plus size={28} color="white" />
        </Animated.View>
      </TouchableOpacity>
      
      {/* Collapse indicator */}
      {isExpanded && (
        <Animated.View
          style={[
            styles.collapseIndicator,
            {
              opacity: panelOpacity,
            },
          ]}
        >
          <TouchableOpacity onPress={toggleExpanded} style={styles.collapseButton}>
            <ChevronDown size={20} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </Animated.View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignItems: 'center',
    zIndex: 1000,
  },
  actionButton: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
    elevation: 8,
  },
  actionTouchable: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 25,
  },
  actionLabel: {
    position: 'absolute',
    right: 60,
    backgroundColor: Colors.light.text,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
    ...shadows.md,
  },
  actionLabelText: {
    color: 'white',
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    whiteSpace: 'nowrap',
  },
  mainButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.lg,
    elevation: 8,
  },
  mainButtonExpanded: {
    backgroundColor: Colors.light.secondary,
  },
  collapseIndicator: {
    position: 'absolute',
    top: -40,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.sm,
    padding: spacing.xs,
    ...shadows.sm,
  },
  collapseButton: {
    padding: spacing.xs,
  },
});
