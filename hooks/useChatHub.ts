import { useState, useCallback, useEffect } from 'react';
import { router } from 'expo-router';
import { useChatStore } from '@/stores/chat-store';
import { useClientStore } from '@/stores/client-store';
import { useInventoryStore } from '@/stores/inventory-store';
import { useServiceStore } from '@/stores/service-store';

export type ChatContext = 'welcome' | 'service' | 'client' | 'inventory' | 'formula' | 'reports';

interface ChatHubState {
  currentContext: ChatContext;
  showQuickActions: boolean;
  showFloatingActions: boolean;
  isProcessingAction: boolean;
  contextData: any;
}

export function useChatHub() {
  const [state, setState] = useState<ChatHubState>({
    currentContext: 'welcome',
    showQuickActions: true,
    showFloatingActions: true,
    isProcessingAction: false,
    contextData: null,
  });

  const { 
    sendMessage, 
    createConversation, 
    activeConversationId 
  } = useChatStore();
  
  const { clients, getClient } = useClientStore();
  const { products, getProduct } = useInventoryStore();
  const { services } = useServiceStore();

  // Update context based on conversation content
  const updateContext = useCallback((newContext: ChatContext, data?: any) => {
    setState(prev => ({
      ...prev,
      currentContext: newContext,
      contextData: data,
      showQuickActions: newContext === 'welcome',
    }));
  }, []);

  // Send a message and update context
  const sendContextualMessage = useCallback(async (
    message: string, 
    context?: ChatContext,
    data?: any
  ) => {
    setState(prev => ({ ...prev, isProcessingAction: true }));
    
    try {
      let conversationId = activeConversationId;
      
      if (!conversationId) {
        conversationId = await createConversation('general');
      }

      await sendMessage(conversationId, message);
      
      if (context) {
        updateContext(context, data);
      }
      
      setState(prev => ({ 
        ...prev, 
        showQuickActions: false,
        showFloatingActions: context !== 'welcome',
      }));
    } catch (error) {
      console.error('Error sending contextual message:', error);
    } finally {
      setState(prev => ({ ...prev, isProcessingAction: false }));
    }
  }, [activeConversationId, createConversation, sendMessage, updateContext]);

  // Quick action handlers
  const handleNewService = useCallback(() => {
    sendContextualMessage(
      'Quiero crear un nuevo servicio de coloración. ¿Puedes guiarme paso a paso?',
      'service'
    );
  }, [sendContextualMessage]);

  const handleQuickPhoto = useCallback(() => {
    sendContextualMessage(
      'Necesito hacer un diagnóstico capilar rápido con una foto. ¿Cómo procedo?',
      'service'
    );
  }, [sendContextualMessage]);

  const handleClientLookup = useCallback((clientId?: string) => {
    if (clientId) {
      const client = getClient(clientId);
      sendContextualMessage(
        `Muéstrame toda la información disponible sobre ${client?.name}`,
        'client',
        client
      );
    } else {
      sendContextualMessage(
        'Muéstrame la lista de mis clientes y su información relevante',
        'client'
      );
    }
  }, [sendContextualMessage, getClient]);

  const handleInventoryCheck = useCallback((productId?: string) => {
    if (productId) {
      const product = getProduct(productId);
      sendContextualMessage(
        `Necesito información detallada sobre ${product?.displayName || 'este producto'}`,
        'inventory',
        product
      );
    } else {
      sendContextualMessage(
        'Quiero revisar el estado de mi inventario, especialmente productos con stock bajo',
        'inventory'
      );
    }
  }, [sendContextualMessage, getProduct]);

  const handleFormulaHelp = useCallback((formulaType?: string) => {
    const message = formulaType 
      ? `Necesito ayuda específica con ${formulaType}`
      : 'Necesito ayuda con una fórmula de coloración. ¿Puedes asistirme?';
    
    sendContextualMessage(message, 'formula');
  }, [sendContextualMessage]);

  const handleReportsRequest = useCallback((reportType?: string) => {
    const message = reportType
      ? `Muéstrame el reporte de ${reportType}`
      : 'Quiero ver un resumen de mi rendimiento y métricas del salón';
    
    sendContextualMessage(message, 'reports');
  }, [sendContextualMessage]);

  // Navigation helpers
  const navigateToService = useCallback((clientId?: string) => {
    if (clientId) {
      router.push(`/service/new?clientId=${clientId}`);
    } else {
      router.push('/service/client-selection');
    }
  }, []);

  const navigateToClient = useCallback((clientId?: string) => {
    if (clientId) {
      router.push(`/client/${clientId}`);
    } else {
      router.push('/clients');
    }
  }, []);

  const navigateToInventory = useCallback((productId?: string) => {
    if (productId) {
      router.push(`/inventory/${productId}`);
    } else {
      router.push('/inventory');
    }
  }, []);

  // Context-aware suggestions
  const getContextualSuggestions = useCallback(() => {
    switch (state.currentContext) {
      case 'service':
        return [
          'Tomar foto del cabello actual',
          'Seleccionar cliente existente',
          'Verificar productos disponibles',
          'Calcular tiempo de procesado',
        ];
      case 'client':
        return [
          'Crear nuevo servicio para este cliente',
          'Ver historial de servicios',
          'Actualizar información de contacto',
          'Agendar próxima cita',
        ];
      case 'inventory':
        return [
          'Añadir nuevo producto',
          'Registrar entrada de stock',
          'Ver productos más utilizados',
          'Configurar alertas de stock bajo',
        ];
      case 'formula':
        return [
          'Especificar marca preferida',
          'Calcular proporciones exactas',
          'Verificar compatibilidad química',
          'Sugerir alternativas',
        ];
      case 'reports':
        return [
          'Ver tendencias mensuales',
          'Analizar satisfacción del cliente',
          'Revisar uso de productos',
          'Comparar con período anterior',
        ];
      default:
        return [
          'Crear nuevo servicio',
          'Buscar cliente',
          'Revisar inventario',
          'Ayuda con fórmula',
        ];
    }
  }, [state.currentContext]);

  // Get contextual data for current context
  const getContextualData = useCallback(() => {
    switch (state.currentContext) {
      case 'client':
        return {
          recentClients: clients.slice(0, 5),
          totalClients: clients.length,
        };
      case 'inventory':
        const lowStockProducts = products.filter(p => p.currentStock <= p.minStock);
        return {
          lowStockProducts,
          totalProducts: products.length,
          lowStockCount: lowStockProducts.length,
        };
      case 'service':
        return {
          recentServices: services.slice(0, 3),
          totalServices: services.length,
        };
      default:
        return null;
    }
  }, [state.currentContext, clients, products, services]);

  return {
    // State
    currentContext: state.currentContext,
    showQuickActions: state.showQuickActions,
    showFloatingActions: state.showFloatingActions,
    isProcessingAction: state.isProcessingAction,
    contextData: state.contextData,

    // Actions
    updateContext,
    sendContextualMessage,
    
    // Quick actions
    handleNewService,
    handleQuickPhoto,
    handleClientLookup,
    handleInventoryCheck,
    handleFormulaHelp,
    handleReportsRequest,

    // Navigation
    navigateToService,
    navigateToClient,
    navigateToInventory,

    // Context helpers
    getContextualSuggestions,
    getContextualData,

    // State setters
    setShowQuickActions: (show: boolean) => 
      setState(prev => ({ ...prev, showQuickActions: show })),
    setShowFloatingActions: (show: boolean) => 
      setState(prev => ({ ...prev, showFloatingActions: show })),
  };
}
