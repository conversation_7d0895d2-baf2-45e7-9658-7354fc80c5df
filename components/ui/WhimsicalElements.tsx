import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  StyleSheet,
  TouchableOpacity,
  Easing,
} from 'react-native';
import {
  <PERSON>rk<PERSON>,
  <PERSON>,
  Star,
  Zap,
  Smile,
  Coffee,
  Palette,
  Scissors,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography } from '@/constants/theme';

interface WhimsicalElementsProps {
  type: 'success' | 'thinking' | 'celebration' | 'welcome' | 'loading';
  message?: string;
  onComplete?: () => void;
}

export default function WhimsicalElements({
  type,
  message,
  onComplete,
}: WhimsicalElementsProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animations = [];

    // Fade in
    animations.push(
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      })
    );

    // Scale in
    animations.push(
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    );

    // Continuous animations based on type
    if (type === 'thinking' || type === 'loading') {
      animations.push(
        Animated.loop(
          Animated.sequence([
            Animated.timing(rotateAnim, {
              toValue: 1,
              duration: 2000,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
            Animated.timing(rotateAnim, {
              toValue: 0,
              duration: 0,
              useNativeDriver: true,
            }),
          ])
        )
      );
    }

    if (type === 'celebration' || type === 'success') {
      animations.push(
        Animated.loop(
          Animated.sequence([
            Animated.timing(sparkleAnim, {
              toValue: 1,
              duration: 1000,
              easing: Easing.inOut(Easing.sine),
              useNativeDriver: true,
            }),
            Animated.timing(sparkleAnim, {
              toValue: 0,
              duration: 1000,
              easing: Easing.inOut(Easing.sine),
              useNativeDriver: true,
            }),
          ])
        )
      );
    }

    if (type === 'welcome') {
      animations.push(
        Animated.loop(
          Animated.sequence([
            Animated.timing(bounceAnim, {
              toValue: 1,
              duration: 1500,
              easing: Easing.inOut(Easing.sine),
              useNativeDriver: true,
            }),
            Animated.timing(bounceAnim, {
              toValue: 0,
              duration: 1500,
              easing: Easing.inOut(Easing.sine),
              useNativeDriver: true,
            }),
          ])
        )
      );
    }

    Animated.parallel(animations).start(() => {
      if (onComplete) {
        setTimeout(onComplete, 2000);
      }
    });
  }, [type]);

  const getElementsForType = () => {
    switch (type) {
      case 'success':
        return {
          icon: Zap,
          color: Colors.light.success,
          emoji: '✨',
          title: '¡Perfecto!',
          subtitle: message || 'Operación completada exitosamente',
          particles: ['💫', '⭐', '✨', '🌟'],
        };
      case 'thinking':
        return {
          icon: Coffee,
          color: Colors.light.primary,
          emoji: '🤔',
          title: 'Pensando...',
          subtitle: message || 'Analizando la mejor solución para ti',
          particles: ['💭', '🧠', '💡', '🔍'],
        };
      case 'celebration':
        return {
          icon: Star,
          color: Colors.light.warning,
          emoji: '🎉',
          title: '¡Increíble!',
          subtitle: message || 'Has completado otro servicio exitoso',
          particles: ['🎊', '🎉', '🥳', '🌈'],
        };
      case 'welcome':
        return {
          icon: Smile,
          color: Colors.light.info,
          emoji: '👋',
          title: '¡Hola!',
          subtitle: message || 'Bienvenido de vuelta, estoy aquí para ayudarte',
          particles: ['👋', '😊', '💜', '🌸'],
        };
      case 'loading':
        return {
          icon: Palette,
          color: Colors.light.secondary,
          emoji: '🎨',
          title: 'Creando magia...',
          subtitle: message || 'Preparando tu fórmula perfecta',
          particles: ['🎨', '✂️', '💄', '🌈'],
        };
      default:
        return {
          icon: Heart,
          color: Colors.light.primary,
          emoji: '💜',
          title: 'Salonier',
          subtitle: message || 'Tu asistente de coloración',
          particles: ['💜', '✨', '🎨', '💫'],
        };
    }
  };

  const elements = getElementsForType();
  const IconComponent = elements.icon;

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const bounceInterpolate = bounceAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -10],
  });

  const sparkleScale = sparkleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.8, 1.2, 0.8],
  });

  const sparkleOpacity = sparkleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 1, 0.3],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      {/* Background particles */}
      <View style={styles.particlesContainer}>
        {elements.particles.map((particle, index) => (
          <Animated.Text
            key={index}
            style={[
              styles.particle,
              {
                left: `${20 + (index * 20)}%`,
                top: `${10 + (index * 15)}%`,
                opacity: sparkleOpacity,
                transform: [{ scale: sparkleScale }],
              },
            ]}
          >
            {particle}
          </Animated.Text>
        ))}
      </View>

      {/* Main content */}
      <View style={styles.content}>
        {/* Main icon with animation */}
        <Animated.View
          style={[
            styles.iconContainer,
            { backgroundColor: elements.color + '20' },
            {
              transform: [
                { rotate: type === 'thinking' || type === 'loading' ? rotateInterpolate : '0deg' },
                { translateY: type === 'welcome' ? bounceInterpolate : 0 },
              ],
            },
          ]}
        >
          <IconComponent size={32} color={elements.color} />
        </Animated.View>

        {/* Emoji overlay */}
        <Text style={styles.emoji}>{elements.emoji}</Text>

        {/* Text content */}
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: elements.color }]}>
            {elements.title}
          </Text>
          <Text style={styles.subtitle}>{elements.subtitle}</Text>
        </View>

        {/* Sparkle effects for celebration */}
        {(type === 'celebration' || type === 'success') && (
          <View style={styles.sparklesContainer}>
            {[...Array(6)].map((_, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.sparkle,
                  {
                    left: `${Math.random() * 80 + 10}%`,
                    top: `${Math.random() * 80 + 10}%`,
                    opacity: sparkleOpacity,
                    transform: [
                      { scale: sparkleScale },
                      { rotate: `${index * 60}deg` },
                    ],
                  },
                ]}
              >
                <Sparkles size={12} color={elements.color} />
              </Animated.View>
            ))}
          </View>
        )}
      </View>

      {/* Interactive element for some types */}
      {type === 'welcome' && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: elements.color }]}
          onPress={onComplete}
          activeOpacity={0.8}
        >
          <Text style={styles.actionButtonText}>¡Empecemos! 🚀</Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    padding: spacing.xl,
    marginVertical: spacing.md,
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  particlesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  particle: {
    position: 'absolute',
    fontSize: 20,
  },
  content: {
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
    position: 'relative',
  },
  emoji: {
    position: 'absolute',
    top: -spacing.sm,
    right: -spacing.sm,
    fontSize: 24,
    zIndex: 2,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: 280,
  },
  sparklesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  sparkle: {
    position: 'absolute',
  },
  actionButton: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: radius.lg,
    marginTop: spacing.md,
  },
  actionButtonText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: 'white',
    textAlign: 'center',
  },
});

// Additional micro-interaction components
export const PulsingDot = ({ color = Colors.light.primary, size = 8 }) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.5,
          duration: 1000,
          easing: Easing.inOut(Easing.sine),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.sine),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  return (
    <Animated.View
      style={{
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: color,
        transform: [{ scale: pulseAnim }],
      }}
    />
  );
};

export const BreathingCard = ({ 
  children, 
  onPress, 
  delay = 0,
  style = {} 
}: {
  children: React.ReactNode;
  onPress?: () => void;
  delay?: number;
  style?: any;
}) => {
  const breatheAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 1.02,
          duration: 2000 + delay,
          easing: Easing.inOut(Easing.sine),
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 1,
          duration: 2000 + delay,
          easing: Easing.inOut(Easing.sine),
          useNativeDriver: true,
        }),
      ])
    );

    setTimeout(() => animation.start(), delay);

    return () => animation.stop();
  }, [delay]);

  const Component = onPress ? TouchableOpacity : View;

  return (
    <Component onPress={onPress} activeOpacity={0.9}>
      <Animated.View
        style={[
          style,
          {
            transform: [{ scale: breatheAnim }],
          },
        ]}
      >
        {children}
      </Animated.View>
    </Component>
  );
};
