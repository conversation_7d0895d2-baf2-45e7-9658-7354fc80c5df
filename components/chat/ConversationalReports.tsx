import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  TrendingUp,
  TrendingDown,
  Users,
  Package,
  DollarSign,
  Calendar,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';

interface MetricCard {
  id: string;
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon: React.ComponentType<any>;
  color: string;
  subtitle?: string;
}

interface ReportData {
  period: string;
  metrics: MetricCard[];
  insights: string[];
  recommendations: string[];
}

interface ConversationalReportsProps {
  reportType: 'daily' | 'weekly' | 'monthly' | 'inventory' | 'clients';
  data: ReportData;
  onMetricPress?: (metricId: string) => void;
  onInsightPress?: (insight: string) => void;
}

export default function ConversationalReports({
  reportType,
  data,
  onMetricPress,
  onInsightPress,
}: ConversationalReportsProps) {

  const getReportTitle = () => {
    switch (reportType) {
      case 'daily':
        return `📊 Reporte Diario - ${data.period}`;
      case 'weekly':
        return `📈 Reporte Semanal - ${data.period}`;
      case 'monthly':
        return `📅 Reporte Mensual - ${data.period}`;
      case 'inventory':
        return `📦 Estado del Inventario`;
      case 'clients':
        return `👥 Análisis de Clientes`;
      default:
        return '📊 Reporte';
    }
  };

  const renderMetricCard = (metric: MetricCard) => {
    const changeIcon = metric.changeType === 'increase' ? TrendingUp : 
                      metric.changeType === 'decrease' ? TrendingDown : null;
    const changeColor = metric.changeType === 'increase' ? Colors.light.success :
                       metric.changeType === 'decrease' ? Colors.light.error :
                       Colors.light.textSecondary;

    return (
      <TouchableOpacity
        key={metric.id}
        style={[styles.metricCard, { borderLeftColor: metric.color }]}
        onPress={() => onMetricPress?.(metric.id)}
        activeOpacity={0.7}
      >
        <View style={styles.metricHeader}>
          <View style={[styles.metricIcon, { backgroundColor: metric.color + '20' }]}>
            <metric.icon size={20} color={metric.color} />
          </View>
          <View style={styles.metricContent}>
            <Text style={styles.metricTitle}>{metric.title}</Text>
            {metric.subtitle && (
              <Text style={styles.metricSubtitle}>{metric.subtitle}</Text>
            )}
          </View>
          {metric.change !== undefined && changeIcon && (
            <View style={styles.changeIndicator}>
              <changeIcon size={16} color={changeColor} />
              <Text style={[styles.changeText, { color: changeColor }]}>
                {metric.change > 0 ? '+' : ''}{metric.change}%
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.metricValue}>
          <Text style={styles.valueText}>{metric.value}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderInsight = (insight: string, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.insightCard}
      onPress={() => onInsightPress?.(insight)}
      activeOpacity={0.7}
    >
      <View style={styles.insightIcon}>
        <TrendingUp size={16} color={Colors.light.info} />
      </View>
      <Text style={styles.insightText}>{insight}</Text>
    </TouchableOpacity>
  );

  const renderRecommendation = (recommendation: string, index: number) => (
    <View key={index} style={styles.recommendationCard}>
      <View style={styles.recommendationIcon}>
        <CheckCircle size={16} color={Colors.light.success} />
      </View>
      <Text style={styles.recommendationText}>{recommendation}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Report Header */}
      <View style={styles.header}>
        <Text style={styles.reportTitle}>{getReportTitle()}</Text>
        <Text style={styles.reportSubtitle}>
          Aquí tienes un resumen de tu rendimiento:
        </Text>
      </View>

      {/* Metrics Grid */}
      <View style={styles.metricsSection}>
        <Text style={styles.sectionTitle}>📈 Métricas Principales</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.metricsScroll}
        >
          {data.metrics.map(renderMetricCard)}
        </ScrollView>
      </View>

      {/* Insights */}
      {data.insights.length > 0 && (
        <View style={styles.insightsSection}>
          <Text style={styles.sectionTitle}>💡 Insights Clave</Text>
          <Text style={styles.sectionSubtitle}>
            He notado algunos patrones interesantes:
          </Text>
          {data.insights.map(renderInsight)}
        </View>
      )}

      {/* Recommendations */}
      {data.recommendations.length > 0 && (
        <View style={styles.recommendationsSection}>
          <Text style={styles.sectionTitle}>🎯 Recomendaciones</Text>
          <Text style={styles.sectionSubtitle}>
            Basándome en los datos, te sugiero:
          </Text>
          {data.recommendations.map(renderRecommendation)}
        </View>
      )}

      {/* Action Prompts */}
      <View style={styles.actionPrompts}>
        <Text style={styles.promptText}>
          ¿Te gustaría que profundice en alguna métrica específica o necesitas ayuda con alguna recomendación?
        </Text>
        
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => onInsightPress?.('Explícame más sobre las tendencias de ventas')}
          >
            <Text style={styles.quickActionText}>Ver Tendencias</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => onInsightPress?.('¿Cómo puedo mejorar la satisfacción del cliente?')}
          >
            <Text style={styles.quickActionText}>Mejorar Satisfacción</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  header: {
    marginBottom: spacing.lg,
  },
  reportTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  reportSubtitle: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
  },
  metricsSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.md,
  },
  metricsScroll: {
    paddingRight: spacing.md,
    gap: spacing.sm,
  },
  metricCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    borderLeftWidth: 4,
    minWidth: 200,
    ...shadows.sm,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  metricIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  metricContent: {
    flex: 1,
  },
  metricTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  metricSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
  },
  changeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  metricValue: {
    alignItems: 'center',
  },
  valueText: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  insightsSection: {
    marginBottom: spacing.lg,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.sm,
  },
  insightIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.info + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  insightText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    lineHeight: 20,
  },
  recommendationsSection: {
    marginBottom: spacing.lg,
  },
  recommendationCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.sm,
  },
  recommendationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.success + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    marginTop: spacing.xs / 2,
  },
  recommendationText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    lineHeight: 20,
  },
  actionPrompts: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    ...shadows.sm,
  },
  promptText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  quickActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: Colors.light.primary + '20',
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
});
