import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Camera,
  Users,
  Package,
  TrendingUp,
  Zap,
  Calendar,
  FileText,
  Palette,
  Clock,
  CheckCircle,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';

interface InlineShortcut {
  id: string;
  title: string;
  subtitle?: string;
  icon: React.ComponentType<any>;
  color: string;
  action: string; // The message to send when pressed
  category: 'service' | 'client' | 'inventory' | 'formula' | 'general';
}

interface InlineShortcutsProps {
  context?: 'welcome' | 'service' | 'client' | 'inventory' | 'formula';
  onShortcutPress: (action: string) => void;
  recentClients?: Array<{ id: string; name: string }>;
  lowStockProducts?: Array<{ id: string; name: string; stock: number }>;
}

export default function InlineShortcuts({
  context = 'welcome',
  onShortcutPress,
  recentClients = [],
  lowStockProducts = [],
}: InlineShortcutsProps) {
  
  const getShortcutsForContext = (): InlineShortcut[] => {
    const baseShortcuts: Record<string, InlineShortcut[]> = {
      welcome: [
        {
          id: 'new-service',
          title: 'Nuevo Servicio',
          subtitle: 'Crear servicio de coloración',
          icon: Zap,
          color: Colors.light.primary,
          action: 'Quiero crear un nuevo servicio de coloración',
          category: 'service',
        },
        {
          id: 'quick-diagnosis',
          title: 'Diagnóstico Rápido',
          subtitle: 'Analizar foto del cabello',
          icon: Camera,
          color: Colors.light.secondary,
          action: 'Necesito hacer un diagnóstico capilar rápido',
          category: 'service',
        },
        {
          id: 'formula-help',
          title: 'Ayuda con Fórmula',
          subtitle: 'Consulta técnica',
          icon: Palette,
          color: Colors.light.success,
          action: 'Necesito ayuda con una fórmula de coloración',
          category: 'formula',
        },
        {
          id: 'client-info',
          title: 'Información de Cliente',
          subtitle: `${recentClients.length} clientes recientes`,
          icon: Users,
          color: Colors.light.info,
          action: 'Muéstrame información de mis clientes recientes',
          category: 'client',
        },
      ],
      service: [
        {
          id: 'take-photo',
          title: 'Tomar Foto',
          subtitle: 'Capturar imagen del cabello',
          icon: Camera,
          color: Colors.light.secondary,
          action: 'Quiero tomar una foto del cabello actual',
          category: 'service',
        },
        {
          id: 'select-client',
          title: 'Seleccionar Cliente',
          subtitle: 'Elegir cliente existente',
          icon: Users,
          color: Colors.light.info,
          action: 'Quiero seleccionar un cliente existente para este servicio',
          category: 'client',
        },
        {
          id: 'check-products',
          title: 'Verificar Productos',
          subtitle: 'Revisar disponibilidad',
          icon: Package,
          color: Colors.light.warning,
          action: 'Necesito verificar la disponibilidad de productos para esta fórmula',
          category: 'inventory',
        },
        {
          id: 'save-service',
          title: 'Guardar Servicio',
          subtitle: 'Completar y guardar',
          icon: CheckCircle,
          color: Colors.light.success,
          action: 'Quiero guardar este servicio como completado',
          category: 'service',
        },
      ],
      client: [
        {
          id: 'new-client',
          title: 'Nuevo Cliente',
          subtitle: 'Registrar cliente nuevo',
          icon: Users,
          color: Colors.light.primary,
          action: 'Quiero registrar un nuevo cliente',
          category: 'client',
        },
        {
          id: 'client-history',
          title: 'Historial de Cliente',
          subtitle: 'Ver servicios anteriores',
          icon: FileText,
          color: Colors.light.info,
          action: 'Muéstrame el historial de servicios de este cliente',
          category: 'client',
        },
        {
          id: 'schedule-appointment',
          title: 'Agendar Cita',
          subtitle: 'Programar próximo servicio',
          icon: Calendar,
          color: Colors.light.secondary,
          action: 'Quiero agendar una cita para este cliente',
          category: 'general',
        },
      ],
      inventory: [
        {
          id: 'add-product',
          title: 'Añadir Producto',
          subtitle: 'Registrar nuevo producto',
          icon: Package,
          color: Colors.light.primary,
          action: 'Quiero añadir un nuevo producto al inventario',
          category: 'inventory',
        },
        {
          id: 'low-stock',
          title: 'Stock Bajo',
          subtitle: `${lowStockProducts.length} productos con stock bajo`,
          icon: TrendingUp,
          color: Colors.light.warning,
          action: 'Muéstrame los productos con stock bajo',
          category: 'inventory',
        },
        {
          id: 'product-usage',
          title: 'Uso de Productos',
          subtitle: 'Ver consumo y tendencias',
          icon: FileText,
          color: Colors.light.info,
          action: 'Quiero ver el reporte de uso de productos',
          category: 'inventory',
        },
      ],
      formula: [
        {
          id: 'brand-specific',
          title: 'Fórmula por Marca',
          subtitle: 'Especificar marca preferida',
          icon: Palette,
          color: Colors.light.primary,
          action: 'Quiero una fórmula específica para una marca en particular',
          category: 'formula',
        },
        {
          id: 'color-correction',
          title: 'Corrección de Color',
          subtitle: 'Solucionar problema de color',
          icon: TrendingUp,
          color: Colors.light.warning,
          action: 'Necesito ayuda para corregir un color que no salió como esperaba',
          category: 'formula',
        },
        {
          id: 'processing-time',
          title: 'Tiempo de Procesado',
          subtitle: 'Calcular tiempos exactos',
          icon: Clock,
          color: Colors.light.info,
          action: 'Necesito calcular los tiempos de procesado para esta fórmula',
          category: 'formula',
        },
      ],
    };

    return baseShortcuts[context] || baseShortcuts.welcome;
  };

  const shortcuts = getShortcutsForContext();

  const renderShortcut = (shortcut: InlineShortcut) => (
    <TouchableOpacity
      key={shortcut.id}
      style={[styles.shortcutCard, { borderLeftColor: shortcut.color }]}
      onPress={() => onShortcutPress(shortcut.action)}
      activeOpacity={0.7}
    >
      <View style={[styles.shortcutIcon, { backgroundColor: shortcut.color + '20' }]}>
        <shortcut.icon size={20} color={shortcut.color} />
      </View>
      <View style={styles.shortcutContent}>
        <Text style={styles.shortcutTitle}>{shortcut.title}</Text>
        {shortcut.subtitle && (
          <Text style={styles.shortcutSubtitle}>{shortcut.subtitle}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  // Dynamic shortcuts based on context data
  const dynamicShortcuts = [];
  
  if (context === 'welcome' && recentClients.length > 0) {
    recentClients.slice(0, 3).forEach(client => {
      dynamicShortcuts.push({
        id: `client-${client.id}`,
        title: client.name,
        subtitle: 'Cliente reciente',
        icon: Users,
        color: Colors.light.info,
        action: `Quiero crear un servicio para ${client.name}`,
        category: 'client' as const,
      });
    });
  }

  if (context === 'inventory' && lowStockProducts.length > 0) {
    lowStockProducts.slice(0, 2).forEach(product => {
      dynamicShortcuts.push({
        id: `product-${product.id}`,
        title: product.name,
        subtitle: `Stock: ${product.stock}`,
        icon: Package,
        color: Colors.light.warning,
        action: `Necesito revisar el stock de ${product.name}`,
        category: 'inventory' as const,
      });
    });
  }

  const allShortcuts = [...shortcuts, ...dynamicShortcuts];

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {allShortcuts.map(renderShortcut)}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.md,
  },
  scrollContent: {
    paddingHorizontal: spacing.sm,
    gap: spacing.sm,
  },
  shortcutCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.sm,
    borderLeftWidth: 3,
    minWidth: 200,
    maxWidth: 250,
    ...shadows.sm,
  },
  shortcutIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  shortcutContent: {
    flex: 1,
  },
  shortcutTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs / 2,
  },
  shortcutSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
});
