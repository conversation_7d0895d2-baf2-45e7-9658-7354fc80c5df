import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import {
  Camera,
  Users,
  Palette,
  Clock,
  CheckCircle,
  ArrowRight,
  Upload,
  Eye,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';

interface ServiceStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed';
  data?: any;
}

interface ConversationalServiceFlowProps {
  currentStep: string;
  steps: ServiceStep[];
  onStepAction: (stepId: string, action: string, data?: any) => void;
  onQuickAction: (action: string) => void;
}

export default function ConversationalServiceFlow({
  currentStep,
  steps,
  onStepAction,
  onQuickAction,
}: ConversationalServiceFlowProps) {

  const getStepIcon = (stepId: string) => {
    switch (stepId) {
      case 'client-selection':
        return Users;
      case 'current-analysis':
        return Camera;
      case 'desired-result':
        return Eye;
      case 'formula-generation':
        return Palette;
      case 'application':
        return Clock;
      case 'completion':
        return CheckCircle;
      default:
        return CheckCircle;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed':
        return Colors.light.success;
      case 'in_progress':
        return Colors.light.primary;
      default:
        return Colors.light.textSecondary;
    }
  };

  const renderStepCard = (step: ServiceStep, index: number) => {
    const StepIcon = getStepIcon(step.id);
    const isActive = step.id === currentStep;
    const isCompleted = step.status === 'completed';
    const color = getStepColor(step.status);

    return (
      <View key={step.id} style={styles.stepContainer}>
        {/* Step connector line */}
        {index > 0 && (
          <View style={[
            styles.stepConnector,
            { backgroundColor: isCompleted ? Colors.light.success : Colors.light.border }
          ]} />
        )}
        
        <TouchableOpacity
          style={[
            styles.stepCard,
            isActive && styles.activeStepCard,
            { borderLeftColor: color }
          ]}
          onPress={() => onStepAction(step.id, 'focus')}
          activeOpacity={0.7}
        >
          <View style={styles.stepHeader}>
            <View style={[styles.stepIcon, { backgroundColor: color + '20' }]}>
              <StepIcon size={20} color={color} />
            </View>
            <View style={styles.stepContent}>
              <Text style={[styles.stepTitle, isActive && styles.activeStepTitle]}>
                {step.title}
              </Text>
              <Text style={styles.stepDescription}>{step.description}</Text>
            </View>
            <View style={styles.stepStatus}>
              {isCompleted && (
                <CheckCircle size={20} color={Colors.light.success} />
              )}
              {step.status === 'in_progress' && (
                <View style={styles.progressIndicator} />
              )}
            </View>
          </View>

          {/* Step-specific content */}
          {isActive && renderStepContent(step)}
        </TouchableOpacity>
      </View>
    );
  };

  const renderStepContent = (step: ServiceStep) => {
    switch (step.id) {
      case 'client-selection':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              ¿Para qué cliente es este servicio?
            </Text>
            <View style={styles.quickActionButtons}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('select-existing-client')}
              >
                <Users size={16} color={Colors.light.primary} />
                <Text style={styles.quickActionText}>Cliente Existente</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('create-new-client')}
              >
                <Users size={16} color={Colors.light.secondary} />
                <Text style={styles.quickActionText}>Nuevo Cliente</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case 'current-analysis':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              Necesito analizar el estado actual del cabello
            </Text>
            <View style={styles.quickActionButtons}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('take-photo')}
              >
                <Camera size={16} color={Colors.light.primary} />
                <Text style={styles.quickActionText}>Tomar Foto</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('upload-photo')}
              >
                <Upload size={16} color={Colors.light.secondary} />
                <Text style={styles.quickActionText}>Subir Foto</Text>
              </TouchableOpacity>
            </View>
            {step.data?.photo && (
              <View style={styles.photoPreview}>
                <Image source={{ uri: step.data.photo }} style={styles.previewImage} />
                <Text style={styles.photoStatus}>Foto cargada ✓</Text>
              </View>
            )}
          </View>
        );

      case 'desired-result':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              ¿Cuál es el resultado deseado?
            </Text>
            <View style={styles.quickActionButtons}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('describe-desired')}
              >
                <Eye size={16} color={Colors.light.primary} />
                <Text style={styles.quickActionText}>Describir</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('reference-photo')}
              >
                <Camera size={16} color={Colors.light.secondary} />
                <Text style={styles.quickActionText}>Foto Referencia</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      case 'formula-generation':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              Generando fórmula personalizada...
            </Text>
            {step.data?.formula && (
              <View style={styles.formulaPreview}>
                <Text style={styles.formulaTitle}>Fórmula Generada:</Text>
                <Text style={styles.formulaText}>{step.data.formula}</Text>
                <View style={styles.formulaActions}>
                  <TouchableOpacity
                    style={styles.formulaActionButton}
                    onPress={() => onStepAction(step.id, 'approve-formula')}
                  >
                    <Text style={styles.formulaActionText}>Aprobar</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.formulaActionButton, styles.secondaryButton]}
                    onPress={() => onStepAction(step.id, 'modify-formula')}
                  >
                    <Text style={[styles.formulaActionText, styles.secondaryButtonText]}>
                      Modificar
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        );

      case 'application':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              Tiempo de aplicación y procesado
            </Text>
            {step.data?.timer && (
              <View style={styles.timerDisplay}>
                <Clock size={24} color={Colors.light.primary} />
                <Text style={styles.timerText}>{step.data.timer}</Text>
              </View>
            )}
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => onQuickAction('start-timer')}
            >
              <Clock size={16} color={Colors.light.primary} />
              <Text style={styles.quickActionText}>Iniciar Timer</Text>
            </TouchableOpacity>
          </View>
        );

      case 'completion':
        return (
          <View style={styles.stepActions}>
            <Text style={styles.actionPrompt}>
              ¡Servicio completado! ¿Cómo quedó el resultado?
            </Text>
            <View style={styles.quickActionButtons}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('take-after-photo')}
              >
                <Camera size={16} color={Colors.light.success} />
                <Text style={styles.quickActionText}>Foto Final</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => onQuickAction('client-feedback')}
              >
                <CheckCircle size={16} color={Colors.light.primary} />
                <Text style={styles.quickActionText}>Feedback</Text>
              </TouchableOpacity>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🎨 Creando Servicio de Coloración</Text>
        <Text style={styles.subtitle}>
          Te guío paso a paso para crear un servicio perfecto
        </Text>
      </View>

      <ScrollView style={styles.stepsContainer} showsVerticalScrollIndicator={false}>
        {steps.map((step, index) => renderStepCard(step, index))}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.helpButton}
          onPress={() => onQuickAction('need-help')}
        >
          <Text style={styles.helpButtonText}>¿Necesitas ayuda?</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  header: {
    marginBottom: spacing.lg,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  stepsContainer: {
    maxHeight: 400,
  },
  stepContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  stepConnector: {
    position: 'absolute',
    left: 20,
    top: -spacing.md,
    width: 2,
    height: spacing.md,
  },
  stepCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    borderLeftWidth: 4,
    ...shadows.sm,
  },
  activeStepCard: {
    borderWidth: 2,
    borderColor: Colors.light.primary + '40',
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs / 2,
  },
  activeStepTitle: {
    color: Colors.light.primary,
  },
  stepDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  stepStatus: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
  },
  stepActions: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  actionPrompt: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  quickActionButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primary + '20',
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    gap: spacing.xs,
  },
  quickActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  photoPreview: {
    alignItems: 'center',
    marginTop: spacing.md,
  },
  previewImage: {
    width: 100,
    height: 100,
    borderRadius: radius.md,
    marginBottom: spacing.sm,
  },
  photoStatus: {
    fontSize: typography.sizes.sm,
    color: Colors.light.success,
  },
  formulaPreview: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    padding: spacing.md,
    marginTop: spacing.sm,
  },
  formulaTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  formulaText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  formulaActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  formulaActionButton: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  formulaActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.surface,
  },
  secondaryButtonText: {
    color: Colors.light.text,
  },
  timerDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  timerText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  footer: {
    marginTop: spacing.lg,
    alignItems: 'center',
  },
  helpButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  helpButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    textDecorationLine: 'underline',
  },
});
