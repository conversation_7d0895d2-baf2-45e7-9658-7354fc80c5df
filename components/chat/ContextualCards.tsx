import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import {
  Users,
  Package,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Phone,
  Mail,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';

interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  lastVisit?: string;
  riskLevel?: string;
  satisfaction?: number;
}

interface Product {
  id: string;
  displayName: string;
  brand: string;
  currentStock: number;
  minStock: number;
  unitType: string;
}

interface Service {
  id: string;
  clientName: string;
  serviceType: string;
  status: string;
  createdAt: string;
  satisfaction?: number;
}

interface ContextualCardsProps {
  type: 'client' | 'product' | 'service' | 'report';
  data: Client | Product | Service | any;
  onPress?: () => void;
  onAction?: (action: string) => void;
}

export default function ContextualCards({
  type,
  data,
  onPress,
  onAction,
}: ContextualCardsProps) {

  const renderClientCard = (client: Client) => (
    <TouchableOpacity
      style={[styles.card, styles.clientCard]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.cardHeader}>
        <View style={styles.clientAvatar}>
          <Users size={20} color={Colors.light.primary} />
        </View>
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>{client.name}</Text>
          <Text style={styles.cardSubtitle}>
            {client.lastVisit ? `Última visita: ${client.lastVisit}` : 'Cliente nuevo'}
          </Text>
        </View>
        {client.riskLevel && (
          <View style={[styles.riskBadge, getRiskBadgeStyle(client.riskLevel)]}>
            <Text style={styles.riskBadgeText}>{client.riskLevel}</Text>
          </View>
        )}
      </View>

      <View style={styles.cardContent}>
        {client.satisfaction && (
          <View style={styles.satisfactionRow}>
            <Star size={16} color={Colors.light.warning} />
            <Text style={styles.satisfactionText}>
              Satisfacción: {Math.round(client.satisfaction * 20)}%
            </Text>
          </View>
        )}
        
        <View style={styles.contactRow}>
          {client.phone && (
            <View style={styles.contactItem}>
              <Phone size={14} color={Colors.light.textSecondary} />
              <Text style={styles.contactText}>{client.phone}</Text>
            </View>
          )}
          {client.email && (
            <View style={styles.contactItem}>
              <Mail size={14} color={Colors.light.textSecondary} />
              <Text style={styles.contactText}>{client.email}</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.cardActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onAction?.('new-service')}
        >
          <Text style={styles.actionButtonText}>Nuevo Servicio</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryActionButton]}
          onPress={() => onAction?.('view-history')}
        >
          <Text style={[styles.actionButtonText, styles.secondaryActionButtonText]}>
            Ver Historial
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderProductCard = (product: Product) => {
    const isLowStock = product.currentStock <= product.minStock;
    
    return (
      <TouchableOpacity
        style={[styles.card, styles.productCard]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.cardHeader}>
          <View style={[styles.productIcon, isLowStock && styles.lowStockIcon]}>
            <Package size={20} color={isLowStock ? Colors.light.warning : Colors.light.success} />
          </View>
          <View style={styles.cardHeaderText}>
            <Text style={styles.cardTitle}>{product.displayName}</Text>
            <Text style={styles.cardSubtitle}>{product.brand}</Text>
          </View>
          {isLowStock && (
            <View style={styles.warningBadge}>
              <AlertTriangle size={16} color={Colors.light.warning} />
            </View>
          )}
        </View>

        <View style={styles.cardContent}>
          <View style={styles.stockRow}>
            <Text style={styles.stockLabel}>Stock actual:</Text>
            <Text style={[
              styles.stockValue,
              isLowStock && styles.lowStockValue
            ]}>
              {product.currentStock} {product.unitType}
            </Text>
          </View>
          <View style={styles.stockRow}>
            <Text style={styles.stockLabel}>Stock mínimo:</Text>
            <Text style={styles.stockValue}>
              {product.minStock} {product.unitType}
            </Text>
          </View>
        </View>

        <View style={styles.cardActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onAction?.('add-stock')}
          >
            <Text style={styles.actionButtonText}>Añadir Stock</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => onAction?.('view-details')}
          >
            <Text style={[styles.actionButtonText, styles.secondaryActionButtonText]}>
              Ver Detalles
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderServiceCard = (service: Service) => (
    <TouchableOpacity
      style={[styles.card, styles.serviceCard]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.cardHeader}>
        <View style={styles.serviceIcon}>
          <CheckCircle size={20} color={Colors.light.success} />
        </View>
        <View style={styles.cardHeaderText}>
          <Text style={styles.cardTitle}>{service.clientName}</Text>
          <Text style={styles.cardSubtitle}>
            {service.serviceType} - {service.status}
          </Text>
        </View>
        <View style={styles.dateBadge}>
          <Clock size={14} color={Colors.light.textSecondary} />
          <Text style={styles.dateText}>{service.createdAt}</Text>
        </View>
      </View>

      <View style={styles.cardActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onAction?.('view-service')}
        >
          <Text style={styles.actionButtonText}>Ver Servicio</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryActionButton]}
          onPress={() => onAction?.('duplicate-service')}
        >
          <Text style={[styles.actionButtonText, styles.secondaryActionButtonText]}>
            Duplicar
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const getRiskBadgeStyle = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'alto':
        return { backgroundColor: Colors.light.error + '20', borderColor: Colors.light.error };
      case 'medio':
        return { backgroundColor: Colors.light.warning + '20', borderColor: Colors.light.warning };
      default:
        return { backgroundColor: Colors.light.success + '20', borderColor: Colors.light.success };
    }
  };

  switch (type) {
    case 'client':
      return renderClientCard(data as Client);
    case 'product':
      return renderProductCard(data as Product);
    case 'service':
      return renderServiceCard(data as Service);
    default:
      return null;
  }
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  clientCard: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  productCard: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.success,
  },
  serviceCard: {
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.info,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  clientAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  productIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.success + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  lowStockIcon: {
    backgroundColor: Colors.light.warning + '20',
  },
  serviceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.success + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  cardHeaderText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs / 2,
  },
  cardSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  riskBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs / 2,
    borderRadius: radius.sm,
    borderWidth: 1,
  },
  riskBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  warningBadge: {
    padding: spacing.xs,
  },
  dateBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
  },
  dateText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  cardContent: {
    marginBottom: spacing.sm,
  },
  satisfactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    gap: spacing.xs,
  },
  satisfactionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
  contactRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
  },
  contactText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  stockRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs / 2,
  },
  stockLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  stockValue: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  lowStockValue: {
    color: Colors.light.warning,
  },
  cardActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  secondaryActionButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.surface,
  },
  secondaryActionButtonText: {
    color: Colors.light.text,
  },
});
