import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
} from 'react-native';
import {
  Users,
  Phone,
  Mail,
  Calendar,
  Star,
  AlertTriangle,
  Plus,
  Search,
  History,
  Edit,
} from 'lucide-react-native';

import Colors from '@/constants/colors';
import { spacing, radius, typography, shadows } from '@/constants/theme';

interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  lastVisit?: string;
  totalServices: number;
  satisfaction: number;
  riskLevel?: 'bajo' | 'medio' | 'alto';
  notes?: string;
}

interface ConversationalClientFlowProps {
  mode: 'list' | 'search' | 'create' | 'detail';
  clients: Client[];
  selectedClient?: Client;
  searchQuery?: string;
  onClientSelect: (client: Client) => void;
  onAction: (action: string, data?: any) => void;
  onSearch: (query: string) => void;
}

export default function ConversationalClientFlow({
  mode,
  clients,
  selectedClient,
  searchQuery = '',
  onClientSelect,
  onAction,
  onSearch,
}: ConversationalClientFlowProps) {

  const renderClientCard = (client: Client) => {
    const riskColor = client.riskLevel === 'alto' ? Colors.light.error :
                     client.riskLevel === 'medio' ? Colors.light.warning :
                     Colors.light.success;

    return (
      <TouchableOpacity
        key={client.id}
        style={[styles.clientCard, { borderLeftColor: riskColor }]}
        onPress={() => onClientSelect(client)}
        activeOpacity={0.7}
      >
        <View style={styles.clientHeader}>
          <View style={styles.clientAvatar}>
            <Users size={20} color={Colors.light.primary} />
          </View>
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{client.name}</Text>
            <Text style={styles.clientSubtitle}>
              {client.totalServices} servicios • Última visita: {client.lastVisit || 'Nunca'}
            </Text>
          </View>
          <View style={styles.clientMeta}>
            <View style={styles.satisfactionRow}>
              <Star size={14} color={Colors.light.warning} />
              <Text style={styles.satisfactionText}>
                {Math.round(client.satisfaction * 20)}%
              </Text>
            </View>
            {client.riskLevel && client.riskLevel !== 'bajo' && (
              <View style={[styles.riskBadge, { backgroundColor: riskColor + '20' }]}>
                <AlertTriangle size={12} color={riskColor} />
                <Text style={[styles.riskText, { color: riskColor }]}>
                  {client.riskLevel.toUpperCase()}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.clientActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onAction('new-service', client)}
          >
            <Plus size={14} color={Colors.light.primary} />
            <Text style={styles.actionText}>Nuevo Servicio</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onAction('view-history', client)}
          >
            <History size={14} color={Colors.light.secondary} />
            <Text style={styles.actionText}>Historial</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onAction('edit-client', client)}
          >
            <Edit size={14} color={Colors.light.info} />
            <Text style={styles.actionText}>Editar</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderClientDetail = (client: Client) => (
    <View style={styles.clientDetail}>
      <View style={styles.detailHeader}>
        <View style={styles.clientAvatar}>
          <Users size={24} color={Colors.light.primary} />
        </View>
        <View style={styles.detailInfo}>
          <Text style={styles.detailName}>{client.name}</Text>
          <Text style={styles.detailSubtitle}>
            Cliente desde {client.lastVisit || 'hace poco'}
          </Text>
        </View>
      </View>

      <View style={styles.detailStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{client.totalServices}</Text>
          <Text style={styles.statLabel}>Servicios</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{Math.round(client.satisfaction * 20)}%</Text>
          <Text style={styles.statLabel}>Satisfacción</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: client.riskLevel === 'alto' ? Colors.light.error : Colors.light.success }]}>
            {client.riskLevel?.toUpperCase() || 'BAJO'}
          </Text>
          <Text style={styles.statLabel}>Riesgo</Text>
        </View>
      </View>

      <View style={styles.contactInfo}>
        {client.phone && (
          <View style={styles.contactItem}>
            <Phone size={16} color={Colors.light.textSecondary} />
            <Text style={styles.contactText}>{client.phone}</Text>
            <TouchableOpacity
              style={styles.contactAction}
              onPress={() => onAction('call-client', client)}
            >
              <Text style={styles.contactActionText}>Llamar</Text>
            </TouchableOpacity>
          </View>
        )}
        {client.email && (
          <View style={styles.contactItem}>
            <Mail size={16} color={Colors.light.textSecondary} />
            <Text style={styles.contactText}>{client.email}</Text>
            <TouchableOpacity
              style={styles.contactAction}
              onPress={() => onAction('email-client', client)}
            >
              <Text style={styles.contactActionText}>Email</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {client.notes && (
        <View style={styles.notesSection}>
          <Text style={styles.notesTitle}>Notas:</Text>
          <Text style={styles.notesText}>{client.notes}</Text>
        </View>
      )}

      <View style={styles.detailActions}>
        <TouchableOpacity
          style={[styles.detailActionButton, styles.primaryAction]}
          onPress={() => onAction('new-service', client)}
        >
          <Plus size={16} color="white" />
          <Text style={styles.primaryActionText}>Nuevo Servicio</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.detailActionButton, styles.secondaryAction]}
          onPress={() => onAction('schedule-appointment', client)}
        >
          <Calendar size={16} color={Colors.light.primary} />
          <Text style={styles.secondaryActionText}>Agendar Cita</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderSearchInterface = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchHeader}>
        <Text style={styles.searchTitle}>🔍 Buscar Cliente</Text>
        <Text style={styles.searchSubtitle}>
          Escribe el nombre, teléfono o email del cliente
        </Text>
      </View>

      <View style={styles.searchInputContainer}>
        <Search size={20} color={Colors.light.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar cliente..."
          value={searchQuery}
          onChangeText={onSearch}
          autoFocus
        />
      </View>

      {searchQuery.length > 0 && (
        <View style={styles.searchResults}>
          <Text style={styles.resultsTitle}>
            {clients.length} resultado{clients.length !== 1 ? 's' : ''} encontrado{clients.length !== 1 ? 's' : ''}
          </Text>
          <ScrollView style={styles.resultsList}>
            {clients.map(renderClientCard)}
          </ScrollView>
        </View>
      )}

      <View style={styles.searchActions}>
        <TouchableOpacity
          style={styles.searchActionButton}
          onPress={() => onAction('create-new-client')}
        >
          <Plus size={16} color={Colors.light.primary} />
          <Text style={styles.searchActionText}>Crear Nuevo Cliente</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderClientsList = () => (
    <View style={styles.listContainer}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>👥 Mis Clientes ({clients.length})</Text>
        <Text style={styles.listSubtitle}>
          Aquí están tus clientes más recientes
        </Text>
      </View>

      <View style={styles.listActions}>
        <TouchableOpacity
          style={styles.listActionButton}
          onPress={() => onAction('search-clients')}
        >
          <Search size={16} color={Colors.light.primary} />
          <Text style={styles.listActionText}>Buscar</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.listActionButton}
          onPress={() => onAction('create-new-client')}
        >
          <Plus size={16} color={Colors.light.success} />
          <Text style={styles.listActionText}>Nuevo</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.clientsList} showsVerticalScrollIndicator={false}>
        {clients.map(renderClientCard)}
      </ScrollView>
    </View>
  );

  switch (mode) {
    case 'search':
      return renderSearchInterface();
    case 'detail':
      return selectedClient ? renderClientDetail(selectedClient) : null;
    case 'list':
    default:
      return renderClientsList();
  }
}

const styles = StyleSheet.create({
  // List styles
  listContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  listHeader: {
    marginBottom: spacing.lg,
  },
  listTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  listSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  listActions: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  listActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    gap: spacing.xs,
    ...shadows.sm,
  },
  listActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  clientsList: {
    maxHeight: 400,
  },

  // Client card styles
  clientCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderLeftWidth: 4,
    ...shadows.sm,
  },
  clientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  clientAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs / 2,
  },
  clientSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  clientMeta: {
    alignItems: 'flex-end',
  },
  satisfactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs / 2,
    marginBottom: spacing.xs / 2,
  },
  satisfactionText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
  riskBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs / 2,
    borderRadius: radius.sm,
    gap: spacing.xs / 2,
  },
  riskText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  clientActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    gap: spacing.xs / 2,
  },
  actionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.text,
  },

  // Search styles
  searchContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  searchHeader: {
    marginBottom: spacing.lg,
  },
  searchTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  searchSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
  },
  searchResults: {
    marginBottom: spacing.lg,
  },
  resultsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  resultsList: {
    maxHeight: 300,
  },
  searchActions: {
    alignItems: 'center',
  },
  searchActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '20',
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    gap: spacing.xs,
  },
  searchActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },

  // Detail styles
  clientDetail: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginVertical: spacing.sm,
    ...shadows.sm,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  detailInfo: {
    flex: 1,
  },
  detailName: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  detailSubtitle: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
  },
  detailStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs / 2,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  contactInfo: {
    marginBottom: spacing.lg,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  contactText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
  contactAction: {
    backgroundColor: Colors.light.primary + '20',
    borderRadius: radius.sm,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
  },
  contactActionText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  notesSection: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  notesTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  notesText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  detailActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  detailActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: radius.md,
    paddingVertical: spacing.md,
    gap: spacing.xs,
  },
  primaryAction: {
    backgroundColor: Colors.light.primary,
  },
  secondaryAction: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  primaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: 'white',
  },
  secondaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
});
